[project]
name = "blitzy_platform_shared"
version = "0.0.1"
description = "Blitzy Platform Shared"
requires-python = ">=3.11"
classifiers = [ "Programming Language :: Python :: 3", "License :: OSI Approved :: MIT License", "Operating System :: OS Independent",]
dependencies = [ "python-json-logger>=3.3.0", "structlog>=25.3.0", "google-api-core>=2.25.0", "google-auth>=2.40.3", "requests>=2.32.3", "PyGithub>=2.6.1", "pydantic>=2.11.5", "langchain-anthropic>=0.3.15", "langchain-aws>=0.2.24", "langchain-core>=0.3.64", "langchain-neo4j>=0.4.0", "langchain-google-vertexai>=2.0.24", "langchain-openai>=0.3.19", "langchain-voyageai>=0.1.6", "langgraph>=0.4.8", "langsmith>=0.3.45", "blitzy-utils>=0.0.236", "tenacity>=8.3.0", "thefuzz>=0.22.1", "transformers>=4.52.4",]
[[project.authors]]
name = "Sid Pardeshi"
email = "<EMAIL>"

