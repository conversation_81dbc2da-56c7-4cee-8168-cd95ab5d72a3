name: Deploy Cloud Run Job

on:
  push:
    branches:
      - qa

concurrency:
  group: qa-deployments
  cancel-in-progress: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: qa
    permissions:
      contents: 'read'
      id-token: 'write'

    env:
      ARTIFACTORY_DOMAIN: us-east1-docker.pkg.dev
      ARTIFACTORY_IMAGE_DOMAIN: us-east1-docker.pkg.dev/${{ vars.PROJECT_ID_STAGE }}/${{ vars.REPOSITORY }}
      IMAGE_NAME: archie-job-code-generator
      IMAGE_TAG: latest
      SERVICE_NAME: archie-job-code-generator
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v1
      with:
        credentials_json: ${{ secrets.GCP_CREDENTIALS_STAGE }}

    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      with:
        project_id: '${{ vars.PROJECT_ID_STAGE }}'

    - name: Configure Docker for GCP Artifactory
      run: |
        gcloud auth configure-docker $ARTIFACTORY_DOMAIN

    - name: Build Docker image
      run: |
        make

    - name: Install deployment utils
      run: make install-deployment-utils

    - name: Tag Docker image
      run: |
        docker tag $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}

    - name: Push Docker image
      run: |
        echo "Pushing $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG"
        echo "Pushing image with SHA $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}"
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }}
        docker push $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:$IMAGE_TAG

    - name: 'Use gcloud CLI'
      run: 'gcloud info'

    - name: Deploy Cloud Run job
      run: |
         gcloud --quiet beta run jobs deploy code-generator \
          --image $ARTIFACTORY_IMAGE_DOMAIN/$IMAGE_NAME:${{ github.sha }} \
          --region ${{ vars.REGION }} \
          --vpc-connector ${{ vars.VPC_CONNECTOR_STAGE }} \
          --vpc-egress ${{ vars.VPC_EGRESS }} \
          --service-account ${{ vars.SERVICE_ACCOUNT_STAGE }} \
          --memory ${{ vars.MEMORY }} \
          --cpu ${{ vars.CPU }} \
          --task-timeout ${{ vars.BETA_TIMEOUT }} \
          --max-retries ${{ vars.MAX_RETRIES }} \
          --set-env-vars "\
            SERVICE_NAME=$SERVICE_NAME,\
            PROJECT_ID=${{ vars.PROJECT_ID_STAGE }},\
            GCS_BUCKET_NAME=${{ vars.GCS_BUCKET_NAME_STAGE }},\
            BLOB_NAME=${{ vars.BLOB_NAME }},\
            PLATFORM_EVENTS_TOPIC=${{ vars.PLATFORM_EVENTS_TOPIC }},\
            UPLOAD_CODE_TOPIC=${{ vars.UPLOAD_CODE_TOPIC }},\
            ANTHROPIC_API_KEY=${{ secrets.ANTHROPIC_API_KEY_DEV }},\
            OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY_DEV }},\
            GOOGLE_API_KEY=${{ secrets.GOOGLE_API_KEY_DEV }},\
            SERVICE_URL_ADMIN=${{ vars.SERVICE_URL_ADMIN_STAGE }},\
            LANGCHAIN_TRACING_V2=${{ vars.LANGCHAIN_TRACING_V2 }},\
            LANGCHAIN_ENDPOINT=${{ vars.LANGCHAIN_ENDPOINT }},\
            LANGCHAIN_API_KEY=${{ secrets.LANGCHAIN_API_KEY_DEV }},\
            LANGCHAIN_PROJECT=${{ vars.LANGCHAIN_PROJECT }}"