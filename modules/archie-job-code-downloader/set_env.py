import json
import os

EVENT_DATA = json.dumps({"job_id": "939a3370-387d-427d-b169-77579fd57cd9", "resume": True, "repo_id": "d9db696a-b353-40f3-9e42-07ccd5f6c947", "team_id": "0e930d20-17c0-4301-9a55-49b8694339d8", "user_id": "2887a75e-9b01-4787-a4a0-bee4e919f4f8", "branch_id": "df042e05-d8b4-47df-97a1-44932e0c4698", "propagate": True, "repo_name": "19J<PERSON>y_<PERSON><PERSON><PERSON>_back_prop", "company_id": "4b01c459-254e-4f28-8b5d-923584f0bf82", "project_id": "6877cbe0-bbfc-442c-975a-0052937b353e", "branch_name": "ADO_Branch_10AM", "tech_spec_id": "22b39eb2-657d-4e0e-8cd2-32386a514842", "head_commit_hash": "4a30f0519248898f6e2f0cb7ab457cd19179f25e", "git_project_repo_id": "36c8d0c2-4d87-4658-920e-68d9c9754ef6", "prev_head_commit_hash": ""}
                        )
os.environ["EVENT_DATA"] = EVENT_DATA

os.environ["GRAPH_CODE_TOPIC"] = "graph-code"
os.environ["PROJECT_ID"] = 'blitzy-platform-stage'
os.environ["GCS_BUCKET_NAME"] = 'blitzy-platform-stage'
os.environ["PRIVATE_BLOB_NAME"] = 'private-src'
os.environ["PLATFORM_EVENTS_TOPIC"] = 'platform-events'

STAGE_SERVICE_URL_ADMIN = "https://archie-service-admin-480762617400.us-central1.run.app"
os.environ["SERVICE_URL_ADMIN"] = STAGE_SERVICE_URL_ADMIN

STAGE_SERVICE_URL_GITHUB = "https://archie-github-handler-480762617400.us-central1.run.app"
os.environ["SERVICE_URL_GITHUB"] = STAGE_SERVICE_URL_GITHUB

STAGE_GITHUB_SECRET_SERVER = "https://archie-secret-manager-480762617400.us-central1.run.app"
os.environ["GITHUB_SECRET_SERVER"] = STAGE_GITHUB_SECRET_SERVER
