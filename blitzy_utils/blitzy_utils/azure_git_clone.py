"""
Azure DevOps Git Clone Implementation

This module provides git command line based repository cloning for Azure DevOps,
replacing API-based file downloads to improve reliability and reduce rate limits.
"""

import os
import re
import shutil
import subprocess
from typing import Optional, List
from urllib.parse import quote

from .common import blitzy_exponential_retry, BlitzyGitFile
from .logger import logger


class AzureDevOpsGitCloneError(Exception):
    """Base exception for Azure DevOps git clone operations."""
    pass


class AzureDevOpsAuthenticationError(AzureDevOpsGitCloneError):
    """Authentication failed during git clone."""
    pass


class AzureDevOpsRepositoryNotFoundError(AzureDevOpsGitCloneError):
    """Repository not found or access denied."""
    pass


class AzureDevOpsNetworkError(AzureDevOpsGitCloneError):
    """Network-related errors during git clone."""
    pass


def build_azure_devops_clone_url(organization: str, project: str, repo_name: str) -> str:
    """
    Build base Azure DevOps clone URL.
    
    Args:
        organization: Azure DevOps organization name
        project: Project name
        repo_name: Repository name
        
    Returns:
        Base clone URL without authentication
        
    Example:
        https://dev.azure.com/myorg/myproject/_git/myrepo
    """
    # URL encode components to handle special characters
    org_encoded = quote(organization, safe='')
    project_encoded = quote(project, safe='')
    repo_encoded = quote(repo_name, safe='')
    
    return f"https://dev.azure.com/{org_encoded}/{project_encoded}/_git/{repo_encoded}"


def add_auth_to_azure_devops_url(base_url: str, access_token: str) -> str:
    """
    Add authentication to Azure DevOps URL.
    
    Args:
        base_url: Base Azure DevOps clone URL
        access_token: Azure DevOps access token (PAT or OAuth)
        
    Returns:
        URL with embedded authentication
        
    Example:
        https://username:<EMAIL>/myorg/myproject/_git/myrepo
    """
    # For Azure DevOps, we can use any username with PAT
    # Common practice is to use empty string or 'oauth' for OAuth tokens
    username = "oauth" if access_token.startswith("oauth") else ""
    
    # URL encode the token to handle special characters
    token_encoded = quote(access_token, safe='')
    
    # Insert authentication into URL
    if "://" in base_url:
        protocol, rest = base_url.split("://", 1)
        if username:
            auth_url = f"{protocol}://{username}:{token_encoded}@{rest}"
        else:
            auth_url = f"{protocol}://:{token_encoded}@{rest}"
    else:
        raise ValueError(f"Invalid URL format: {base_url}")
    
    return auth_url


def sanitize_url_for_logging(url: str) -> str:
    """
    Remove credentials from URL for safe logging.
    
    Args:
        url: URL that may contain credentials
        
    Returns:
        URL with credentials removed
    """
    # Remove any authentication information from URL for logging
    return re.sub(r'://[^@]*@', '://', url)


@blitzy_exponential_retry()
def clone_azure_devops_repository_with_auth(
    organization: str,
    project: str,
    repo_name: str,
    access_token: str,
    clone_path: str,
    branch_name: str = "main",
    commit_hash: Optional[str] = None
) -> bool:
    """
    Clone Azure DevOps repository using git command line with authentication.
    
    Args:
        organization: Azure DevOps organization name
        project: Project name
        repo_name: Repository name
        access_token: Azure DevOps access token
        clone_path: Local path where to clone the repository
        branch_name: Branch to clone (default: "main")
        commit_hash: Specific commit to checkout after cloning
        
    Returns:
        True if successful, False otherwise
        
    Raises:
        AzureDevOpsAuthenticationError: Authentication failed
        AzureDevOpsRepositoryNotFoundError: Repository not found
        AzureDevOpsNetworkError: Network-related errors
    """
    try:
        # Clean up existing directory if it exists
        if os.path.exists(clone_path):
            logger.info(f"Removing existing directory: {clone_path}")
            shutil.rmtree(clone_path)

        # Create parent directories if they don't exist
        os.makedirs(clone_path, exist_ok=True)

        # Build clone URL with authentication
        base_url = build_azure_devops_clone_url(organization, project, repo_name)
        clone_url = add_auth_to_azure_devops_url(base_url, access_token)
        
        # Log sanitized URL for debugging
        safe_url = sanitize_url_for_logging(clone_url)
        logger.info(f"Cloning Azure DevOps repository: {safe_url} to {clone_path}")

        # Set up environment for git command
        env = os.environ.copy()
        env['GIT_TERMINAL_PROMPT'] = '0'
        env['GIT_ASKPASS'] = 'echo'
        env['GCM_INTERACTIVE'] = 'never'
        env['GIT_CREDENTIAL_HELPER'] = ''

        # Build git clone command
        clone_cmd = [
            "git",
            "-c", "credential.helper=",
            "-c", "core.askpass=",
            "-c", "credential.interactive=never",
            "clone"
        ]

        # Add branch specification if no specific commit is requested
        if branch_name and not commit_hash:
            clone_cmd.extend(["-b", branch_name])

        # Add repository URL and destination
        clone_cmd.extend([clone_url, clone_path])

        # Execute clone command
        result = subprocess.run(clone_cmd, capture_output=True, text=True, env=env, check=False)
        
        if result.returncode != 0:
            error_msg = result.stderr.strip()
            safe_error = sanitize_url_for_logging(error_msg)
            logger.error(f"Error cloning Azure DevOps repository: {safe_error}")
            
            # Classify error types for better handling
            if "authentication failed" in error_msg.lower() or "401" in error_msg:
                raise AzureDevOpsAuthenticationError(f"Authentication failed: {safe_error}")
            elif "repository not found" in error_msg.lower() or "404" in error_msg:
                raise AzureDevOpsRepositoryNotFoundError(f"Repository not found: {safe_error}")
            elif "network" in error_msg.lower() or "timeout" in error_msg.lower():
                raise AzureDevOpsNetworkError(f"Network error: {safe_error}")
            else:
                raise AzureDevOpsGitCloneError(f"Git clone failed: {safe_error}")

        logger.info(f"Azure DevOps repository cloned successfully to: {clone_path}")

        # Configure git to not use credential helpers for this repository
        subprocess.run(
            ["git", "-C", clone_path, "config", "--local", "credential.helper", ""],
            capture_output=True,
            check=False
        )

        # If specific commit hash is provided, checkout that commit
        if commit_hash:
            logger.info(f"Checking out specific commit: {commit_hash}")
            checkout_cmd = ["git", "-c", "credential.helper=", "-C", clone_path, "checkout", commit_hash]
            checkout_result = subprocess.run(checkout_cmd, capture_output=True, text=True, env=env, check=False)

            if checkout_result.returncode != 0:
                error_msg = checkout_result.stderr.strip()
                logger.error(f"Error checking out commit {commit_hash}: {error_msg}")
                raise AzureDevOpsGitCloneError(f"Failed to checkout commit {commit_hash}: {error_msg}")
            
            logger.info(f"Checked out commit: {commit_hash}")

        return True

    except (AzureDevOpsAuthenticationError, AzureDevOpsRepositoryNotFoundError, 
            AzureDevOpsNetworkError, AzureDevOpsGitCloneError):
        # Re-raise our custom exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error during Azure DevOps repository clone: {str(e)}")
        raise AzureDevOpsGitCloneError(f"Unexpected error: {str(e)}")


def get_azure_devops_git_clone_config() -> dict:
    """
    Get Azure DevOps git clone configuration from environment variables.
    
    Returns:
        Configuration dictionary with git clone settings
    """
    return {
        'use_git_clone': os.getenv('AZURE_DEVOPS_USE_GIT_CLONE', 'true').lower() == 'true',
        'fallback_to_api': os.getenv('AZURE_DEVOPS_FALLBACK_TO_API', 'true').lower() == 'true',
        'clone_timeout': int(os.getenv('AZURE_DEVOPS_GIT_CLONE_TIMEOUT', '300')),
        'cleanup_after_processing': os.getenv('AZURE_DEVOPS_CLEANUP_CLONED_REPOS', 'true').lower() == 'true'
    }
