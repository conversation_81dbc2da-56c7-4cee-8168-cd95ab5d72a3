"""
Test suite for Azure DevOps git clone functionality.

This module tests the new git clone implementation for Azure DevOps repositories,
including URL construction, authentication, and error handling.
"""

import pytest
import os
import tempfile
import shutil
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Optional

# Import the functions we want to test
try:
    from blitzy_utils.azure_git_clone import (
        build_azure_devops_clone_url,
        add_auth_to_azure_devops_url,
        sanitize_url_for_logging,
        clone_azure_devops_repository_with_auth,
        get_azure_devops_git_clone_config,
        AzureDevOpsGitCloneError,
        AzureDevOpsAuthenticationError,
        AzureDevOpsRepositoryNotFoundError,
        AzureDevOpsNetworkError
    )
    from blitzy_utils.azure import (
        _download_all_git_files_to_disk_azure_devops,
        _download_azure_devops_with_git_clone,
        GitProjectRepo
    )
    from blitzy_utils.common import BlitzyGitFile
except ImportError as e:
    pytest.skip(f"Could not import Azure DevOps git clone functions: {e}", allow_module_level=True)


class TestAzureDevOpsUrlUtils:
    """Test cases for Azure DevOps URL construction utilities."""

    def test_build_azure_devops_clone_url_basic(self):
        """Test basic URL construction."""
        url = build_azure_devops_clone_url("myorg", "myproject", "myrepo")
        expected = "https://dev.azure.com/myorg/myproject/_git/myrepo"
        assert url == expected

    def test_build_azure_devops_clone_url_with_special_chars(self):
        """Test URL construction with special characters."""
        url = build_azure_devops_clone_url("my-org", "my project", "my@repo")
        expected = "https://dev.azure.com/my-org/my%20project/_git/my%40repo"
        assert url == expected

    def test_add_auth_to_azure_devops_url_with_pat(self):
        """Test adding PAT authentication to URL."""
        base_url = "https://dev.azure.com/myorg/myproject/_git/myrepo"
        token = "mypat123"
        auth_url = add_auth_to_azure_devops_url(base_url, token)
        expected = "https://:<EMAIL>/myorg/myproject/_git/myrepo"
        assert auth_url == expected

    def test_add_auth_to_azure_devops_url_with_oauth(self):
        """Test adding OAuth authentication to URL."""
        base_url = "https://dev.azure.com/myorg/myproject/_git/myrepo"
        token = "oauth_token_123"
        auth_url = add_auth_to_azure_devops_url(base_url, token)
        expected = "https://oauth:<EMAIL>/myorg/myproject/_git/myrepo"
        assert auth_url == expected

    def test_add_auth_to_azure_devops_url_invalid_url(self):
        """Test error handling for invalid URL."""
        with pytest.raises(ValueError, match="Invalid URL format"):
            add_auth_to_azure_devops_url("invalid-url", "token")

    def test_sanitize_url_for_logging(self):
        """Test URL sanitization for logging."""
        auth_url = "https://user:<EMAIL>/org/project/_git/repo"
        sanitized = sanitize_url_for_logging(auth_url)
        expected = "https://dev.azure.com/org/project/_git/repo"
        assert sanitized == expected

    def test_sanitize_url_for_logging_no_auth(self):
        """Test URL sanitization when no auth is present."""
        url = "https://dev.azure.com/org/project/_git/repo"
        sanitized = sanitize_url_for_logging(url)
        assert sanitized == url


class TestAzureDevOpsGitCloneConfig:
    """Test cases for Azure DevOps git clone configuration."""

    def test_get_azure_devops_git_clone_config_defaults(self):
        """Test default configuration values."""
        with patch.dict(os.environ, {}, clear=True):
            config = get_azure_devops_git_clone_config()
            assert config['use_git_clone'] is True
            assert config['fallback_to_api'] is True
            assert config['clone_timeout'] == 300
            assert config['cleanup_after_processing'] is True

    def test_get_azure_devops_git_clone_config_custom(self):
        """Test custom configuration from environment variables."""
        env_vars = {
            'AZURE_DEVOPS_USE_GIT_CLONE': 'false',
            'AZURE_DEVOPS_FALLBACK_TO_API': 'false',
            'AZURE_DEVOPS_GIT_CLONE_TIMEOUT': '600',
            'AZURE_DEVOPS_CLEANUP_CLONED_REPOS': 'false'
        }
        with patch.dict(os.environ, env_vars):
            config = get_azure_devops_git_clone_config()
            assert config['use_git_clone'] is False
            assert config['fallback_to_api'] is False
            assert config['clone_timeout'] == 600
            assert config['cleanup_after_processing'] is False


class TestAzureDevOpsGitClone:
    """Test cases for Azure DevOps git clone functionality."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir, ignore_errors=True)

    @pytest.fixture
    def mock_git_project_repo(self):
        """Mock GitProjectRepo object for testing."""
        return GitProjectRepo(
            access_token="mock_token",
            azure_org_id="mock_org_id",
            azure_org_name="mock_org",
            azure_project_id="mock_project_id",
            repo_id="mock_repo_id"
        )

    @patch('blitzy_utils.azure_git_clone.subprocess.run')
    @patch('blitzy_utils.azure_git_clone.os.path.exists')
    @patch('blitzy_utils.azure_git_clone.os.makedirs')
    def test_clone_azure_devops_repository_with_auth_success(
        self, mock_makedirs, mock_exists, mock_subprocess, temp_dir
    ):
        """Test successful repository cloning."""
        # Setup mocks
        mock_exists.return_value = False
        mock_result = Mock()
        mock_result.returncode = 0
        mock_result.stderr = ""
        mock_subprocess.return_value = mock_result

        # Test the function
        success = clone_azure_devops_repository_with_auth(
            organization="testorg",
            project="testproject",
            repo_name="testrepo",
            access_token="testtoken",
            clone_path=temp_dir,
            branch_name="main"
        )

        assert success is True
        mock_makedirs.assert_called_once()
        assert mock_subprocess.call_count >= 1  # At least one call for clone

    @patch('blitzy_utils.azure_git_clone.subprocess.run')
    def test_clone_azure_devops_repository_auth_error(self, mock_subprocess, temp_dir):
        """Test authentication error handling."""
        # Setup mock to simulate auth failure
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stderr = "authentication failed"
        mock_subprocess.return_value = mock_result

        with pytest.raises(AzureDevOpsAuthenticationError):
            clone_azure_devops_repository_with_auth(
                organization="testorg",
                project="testproject",
                repo_name="testrepo",
                access_token="badtoken",
                clone_path=temp_dir
            )

    @patch('blitzy_utils.azure_git_clone.subprocess.run')
    def test_clone_azure_devops_repository_not_found_error(self, mock_subprocess, temp_dir):
        """Test repository not found error handling."""
        # Setup mock to simulate repo not found
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stderr = "repository not found"
        mock_subprocess.return_value = mock_result

        with pytest.raises(AzureDevOpsRepositoryNotFoundError):
            clone_azure_devops_repository_with_auth(
                organization="testorg",
                project="testproject",
                repo_name="nonexistent",
                access_token="token",
                clone_path=temp_dir
            )

    @patch('blitzy_utils.azure_git_clone.subprocess.run')
    def test_clone_azure_devops_repository_network_error(self, mock_subprocess, temp_dir):
        """Test network error handling."""
        # Setup mock to simulate network error
        mock_result = Mock()
        mock_result.returncode = 1
        mock_result.stderr = "network timeout"
        mock_subprocess.return_value = mock_result

        with pytest.raises(AzureDevOpsNetworkError):
            clone_azure_devops_repository_with_auth(
                organization="testorg",
                project="testproject",
                repo_name="testrepo",
                access_token="token",
                clone_path=temp_dir
            )


class TestAzureDevOpsIntegration:
    """Test cases for Azure DevOps integration with existing functions."""

    @pytest.fixture
    def mock_git_project_repo(self):
        """Mock GitProjectRepo object for testing."""
        return GitProjectRepo(
            access_token="mock_token",
            azure_org_id="mock_org_id",
            azure_org_name="mock_org",
            azure_project_id="mock_project_id",
            repo_id="mock_repo_id"
        )

    @patch('blitzy_utils.azure._setup_repository_access')
    @patch('blitzy_utils.azure._get_commit_hash')
    @patch('blitzy_utils.azure._download_azure_devops_with_git_clone')
    @patch('blitzy_utils.azure.get_azure_devops_git_clone_config')
    def test_download_all_git_files_with_git_clone_enabled(
        self, mock_config, mock_git_clone, mock_get_commit, mock_setup, mock_git_project_repo
    ):
        """Test main download function with git clone enabled."""
        # Setup mocks
        mock_config.return_value = {'use_git_clone': True, 'fallback_to_api': True}
        mock_setup.return_value = mock_git_project_repo
        mock_get_commit.return_value = "abc123"
        mock_git_clone.return_value = [BlitzyGitFile(path="test.py", text="content")]

        # Test the function
        result = _download_all_git_files_to_disk_azure_devops(
            repo_name="testrepo",
            branch_name="main",
            commit_hash="abc123",
            git_project_repo_id="test_id"
        )

        assert len(result) == 1
        assert result[0].path == "test.py"
        mock_git_clone.assert_called_once()

    @patch('blitzy_utils.azure._setup_repository_access')
    @patch('blitzy_utils.azure._get_commit_hash')
    @patch('blitzy_utils.azure._download_azure_devops_with_git_clone')
    @patch('blitzy_utils.azure._process_repository_files')
    @patch('blitzy_utils.azure.get_azure_devops_git_clone_config')
    def test_download_all_git_files_with_fallback(
        self, mock_config, mock_api_download, mock_git_clone, mock_get_commit, mock_setup, mock_git_project_repo
    ):
        """Test main download function with git clone failure and API fallback."""
        # Setup mocks
        mock_config.return_value = {'use_git_clone': True, 'fallback_to_api': True}
        mock_setup.return_value = mock_git_project_repo
        mock_get_commit.return_value = "abc123"
        mock_git_clone.side_effect = AzureDevOpsGitCloneError("Clone failed")
        mock_api_download.return_value = [BlitzyGitFile(path="test.py", text="content")]

        # Test the function
        result = _download_all_git_files_to_disk_azure_devops(
            repo_name="testrepo",
            branch_name="main",
            commit_hash="abc123",
            git_project_repo_id="test_id"
        )

        assert len(result) == 1
        mock_git_clone.assert_called_once()
        mock_api_download.assert_called_once()


if __name__ == "__main__":
    pytest.main([__file__])
